#pragma once

#include <cstdint>
#include <vector>
#include <memory>

// Forward declarations
namespace jit {
    enum class Status;
    class JitCache;
    class JitTranslator;
}

namespace memory {
    class MemoryManager;
}

namespace x86_64 {

class DecodedInstruction;

enum class ExecutionUnit {
    INTEGER,
    FLOATING_POINT,
    VECTOR,
    LOAD_STORE
};

enum class InstructionType {
    INTEGER_ALU,
    INTEGER_MUL,
    FLOAT_ADD,
    FLOAT_MUL,
    VECTOR_ADD,
    VECTOR_MUL,
    LOAD,
    STORE
};

struct PipelineStage {
    DecodedInstruction instruction;
    uint64_t pc;
    int cycle;
};

class X86_64Pipeline {
private:
    memory::MemoryManager& memory_;
    jit::JitCache& jitCache_;
    jit::JitTranslator& translator_;
    int cpuId_;
    uint64_t pc_;

    std::vector<PipelineStage> executeStage_;
    std::vector<PipelineStage> memoryStage_;

public:
    X86_64Pipeline(memory::MemoryManager& memory,
                   jit::JitCache& cache,
                   jit::JitTranslator& translator,
                   int cpuId);

    bool translateAndCache(uint64_t pc);
    bool HasWAWHazard(const DecodedInstruction& instr);
    bool HasWARHazard(const DecodedInstruction& instr);
    ExecutionUnit GetRequiredExecutionUnit(const DecodedInstruction& instr);
    int GetInstructionLatency(const DecodedInstruction& instr);
};

} // namespace x86_64